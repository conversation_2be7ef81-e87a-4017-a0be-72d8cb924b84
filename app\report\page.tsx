"use client"

import styles from "./page.module.scss";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import JC_Title from "../components/JC_Title/JC_Title";
import <PERSON><PERSON>_Button from "../components/JC_Button/JC_Button";
import JC_Spinner from "../components/JC_Spinner/JC_Spinner";
import { JC_Post } from "../apiServices/JC_Post";
import { LocalStorageKeyEnum } from "../enums/LocalStorageKey";
import { PropertyModel } from "../models/Property";

export default function ReportPage() {
    // - STATE - //
    const router = useRouter();
    const [selectedProperty, setSelectedProperty] = useState<PropertyModel | null>(null);
    const [noPropertySelected, setNoPropertySelected] = useState<boolean>(false);
    const [isGenerating, setIsGenerating] = useState<boolean>(false);
    const [generatedPdfUrl, setGeneratedPdfUrl] = useState<string | null>(null);
    const [generatedPdfData, setGeneratedPdfData] = useState<{
        pdfData: string;
        filename: string;
        contentType: string;
    } | null>(null);

    // - EFFECTS - //
    useEffect(() => {
        checkSelectedProperty();
    }, []);

    // - FUNCTIONS - //
    const checkSelectedProperty = async () => {
        try {
            const selectedPropertyId = localStorage.getItem(LocalStorageKeyEnum.JC_SelectedCustomer);

            if (!selectedPropertyId) {
                setNoPropertySelected(true);
                return;
            }

            // Fetch property details to verify it exists
            const property = await PropertyModel.Get(selectedPropertyId);

            if (property) {
                setSelectedProperty(property);
                setNoPropertySelected(false);
            } else {
                // Property not found, clear localStorage
                localStorage.removeItem(LocalStorageKeyEnum.JC_SelectedCustomer);
                setNoPropertySelected(true);
            }
        } catch (error) {
            console.error('Error checking selected property:', error);
            // Clear localStorage if there's an error
            localStorage.removeItem(LocalStorageKeyEnum.JC_SelectedCustomer);
            setNoPropertySelected(true);
        }
    };

    // Handle Customers button click
    const handlePropertiesClick = () => {
        router.push('/customer');
    };

    // Handle Generate Report button click
    const handleGenerateReport = async () => {
        if (!selectedProperty?.Id) return;

        try {
            setIsGenerating(true);

            // Generate PDF using JC_Post
            const result = await JC_Post('pdf/generateInspectionReport', { propertyId: selectedProperty.Id });

            // Store the PDF data
            setGeneratedPdfData(result);

            // Convert base64 PDF data to blob and create URL for viewing
            const base64Data = result.pdfData;
            const binaryString = atob(base64Data);
            const bytes = new Uint8Array(binaryString.length);
            for (let i = 0; i < binaryString.length; i++) {
                bytes[i] = binaryString.charCodeAt(i);
            }
            const blob = new Blob([bytes], { type: result.contentType });
            const url = URL.createObjectURL(blob);
            setGeneratedPdfUrl(url);

        } catch (error) {
            console.error('Error generating PDF:', error);
            // You might want to show a toast error here
        } finally {
            setIsGenerating(false);
        }
    };

    // Handle Download button click
    const handleDownload = () => {
        if (!generatedPdfData) return;

        // Convert base64 PDF data to blob
        const base64Data = generatedPdfData.pdfData;
        const binaryString = atob(base64Data);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i);
        }
        const blob = new Blob([bytes], { type: generatedPdfData.contentType });

        // Create download link
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = generatedPdfData.filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Clean up the URL
        setTimeout(() => URL.revokeObjectURL(url), 1000);
    };

    // - RENDER - //
    if (noPropertySelected) {
        return (
            <div className={styles.noPropertyContainer}>
                <JC_Title title="Select a Customer" />
                <JC_Button
                    text="Customers"
                    onClick={handlePropertiesClick}
                />
            </div>
        );
    }

    if (!selectedProperty) {
        return <JC_Spinner isPageBody />;
    }

    return (
        <div className={styles.mainContainer}>
            <JC_Title title={`Generate Report - ${selectedProperty.Name}`} />
            
            {/* PDF Viewer Area */}
            <div className={styles.pdfViewerArea}>
                {!generatedPdfUrl ? (
                    <div className={styles.waitingMessage}>
                        {isGenerating ? "Generating report..." : "Waiting for report..."}
                    </div>
                ) : (
                    <iframe
                        src={generatedPdfUrl}
                        className={styles.pdfViewer}
                        title="Generated Report"
                    />
                )}
            </div>

            {/* Footer */}
            <div className={styles.footer}>
                <div className={styles.leftFooterContainer}>
                    {/* Empty for now, matching JC_FormTablet structure */}
                </div>
                <div className={styles.rightFooterContainer}>
                    <JC_Button
                        text="Generate Report"
                        onClick={handleGenerateReport}
                        isLoading={isGenerating}
                    />
                    <JC_Button
                        text="Download"
                        onClick={handleDownload}
                        disabled={!generatedPdfData}
                    />
                </div>
            </div>
        </div>
    );
}
