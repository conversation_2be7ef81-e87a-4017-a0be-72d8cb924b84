@import '../global';

// - MAIN CONTAINER - //

.mainContainer {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100%;
    background-color: $offWhite;
    overflow: hidden;
}

// - CONTENT SECTIONS - //

.noPropertyContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    width: 100%;
    background-color: $offWhite;
    gap: 20px;
}

.pdfViewerArea {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 20px;
    background-color: $white;
    border: $smallBorderWidth solid $primaryColor;
    border-radius: $tinyBorderRadius;
    overflow: hidden;
    position: relative;
}

.waitingMessage {
    color: $secondaryColor;
    font-size: 18px;
    text-align: center;
    padding: 40px;
}

.pdfViewer {
    width: 100%;
    height: 100%;
    border: none;
    background-color: $white;
}

// - FOOTER - //

.footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: $white;
    border-top: $smallBorderWidth solid $primaryColor;
    min-height: 60px;
    gap: 15px;

    .leftFooterContainer {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .rightFooterContainer {
        display: flex;
        align-items: center;
        gap: 15px;
    }
}

// - SCREEN SIZES - //

@media (max-width: $mediumScreenSize) {
    .mainContainer {
        height: 100vh;
    }

    .pdfViewerArea {
        margin: 15px;
    }

    .footer {
        padding: 12px 18px;
        gap: 12px;
    }

    .waitingMessage {
        font-size: 16px;
        padding: 30px;
    }
}

@media (max-width: $smallScreenSize) {
    .pdfViewerArea {
        margin: 10px;
    }

    .footer {
        padding: 10px 15px;
        flex-direction: column;
        gap: 10px;

        .leftFooterContainer,
        .rightFooterContainer {
            width: 100%;
            justify-content: center;
        }
    }

    .waitingMessage {
        font-size: 16px;
        padding: 20px;
    }
}

@media (max-width: $tinyScreenSize) {
    .pdfViewerArea {
        margin: 8px;
    }

    .waitingMessage {
        font-size: 14px;
        padding: 15px;
    }
}
