"use client"

import styles from "./page.module.scss";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import J<PERSON>_Title from "../components/JC_Title/JC_Title";
import <PERSON><PERSON>_Button from "../components/JC_Button/JC_Button";
import J<PERSON>_Spinner from "../components/JC_Spinner/JC_Spinner";
import { PropertyModel } from "../models/Property";
import { LocalStorageKeyEnum } from "../enums/LocalStorageKey";

export default function DefectsPage() {
    const router = useRouter();

    // - STATE - //
    const [noPropertySelected, setNoPropertySelected] = useState<boolean>(false);

    // - EFFECTS - //
    useEffect(() => {
        checkForSelectedProperty();
    }, []);

    // Check if there's a selected property in localStorage
    const checkForSelectedProperty = async () => {
        try {
            const selectedPropertyId = localStorage.getItem(LocalStorageKeyEnum.JC_SelectedCustomer);

            if (selectedPropertyId) {
                // Check if this property exists and is not deleted
                const property = await PropertyModel.Get(selectedPropertyId);

                if (property && !property.Deleted) {
                    // Redirect to defects edit page with this property
                    router.push(`/defects/edit/${selectedPropertyId}`);
                    return;
                } else {
                    // Property doesn't exist or is deleted, clear localStorage
                    localStorage.removeItem(LocalStorageKeyEnum.JC_SelectedCustomer);
                }
            }

            // No property selected or property not found
            setNoPropertySelected(true);
        } catch (error) {
            console.error('Error checking selected property:', error);
            // Clear localStorage if there's an error
            localStorage.removeItem(LocalStorageKeyEnum.JC_SelectedCustomer);
            setNoPropertySelected(true);
        }
    };

    // Handle Customers button click
    const handlePropertiesClick = () => {
        router.push('/customer');
    };

    // - RENDER - //
    if (!noPropertySelected) {
        return <JC_Spinner isPageBody />;
    }

    return (
        <div className={styles.mainContainer}>
            <JC_Title title="Select a Customer" />
            <JC_Button
                text="Customers"
                onClick={handlePropertiesClick}
            />
        </div>
    );
}
