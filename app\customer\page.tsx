"use client"

import styles from "./page.module.scss";
import { useEffect, useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import JC_List from "../components/JC_List/JC_List";
import JC_Button from "../components/JC_Button/JC_Button";
import JC_Title from "../components/JC_Title/JC_Title";
import JC_Spinner from "../components/JC_Spinner/JC_Spinner";
import JC_FormTablet, { JC_FormTabletModel } from "../components/JC_FormTablet/JC_FormTablet";
import { PropertyModel } from "../models/Property";
import { O_BuildingTypeModel } from "../models/O_BuildingType";
import { JC_Utils } from "../Utils";
import { JC_ListHeader } from "../components/JC_List/JC_ListHeader";
import { LocalStorageKeyEnum } from "../enums/LocalStorageKey";
import { FieldTypeEnum } from "../enums/FieldType";

export default function CustomerPage() {
    const router = useRouter();

    // - STATE - //
    const [initialised, setInitialised] = useState<boolean>(false);
    const [properties, setProperties] = useState<PropertyModel[]>([]);
    const [buildingTypes, setBuildingTypes] = useState<any[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [selectedCustomer, setSelectedCustomer] = useState<PropertyModel | null>(null);

    // - EFFECTS - //
    useEffect(() => {
        loadData();
        checkForSelectedCustomer();
    }, []);

    // Check if there's a selected customer in localStorage
    const checkForSelectedCustomer = async () => {
        try {
            const selectedCustomerId = localStorage.getItem(LocalStorageKeyEnum.JC_SelectedCustomer);

            if (selectedCustomerId) {
                // Check if this customer exists and is not deleted
                const customer = await PropertyModel.Get(selectedCustomerId);

                if (customer && !customer.Deleted) {
                    setSelectedCustomer(customer);
                    return;
                } else {
                    // Customer doesn't exist or is deleted, clear localStorage
                    localStorage.removeItem(LocalStorageKeyEnum.JC_SelectedCustomer);
                }
            }
        } catch (error) {
            console.error('Error checking selected customer:', error);
            // Clear localStorage if there's an error
            localStorage.removeItem(LocalStorageKeyEnum.JC_SelectedCustomer);
        }
    };

    // - LOAD DATA - //
    const loadData = useCallback(async () => {
        try {
            setIsLoading(true);

            // Load properties and building types for display
            const [propertiesData, buildingTypesData] = await Promise.all([
                PropertyModel.GetList(),
                O_BuildingTypeModel.GetList()
            ]);

            setProperties(propertiesData || []);
            setBuildingTypes(buildingTypesData || []);
        } catch (error) {
            console.error('Error loading data:', error);
        } finally {
            setIsLoading(false);
            setInitialised(true);
        }
    }, []);

    // - HANDLERS - //
    const handleCustomerClick = (customer: PropertyModel) => {
        localStorage.setItem(LocalStorageKeyEnum.JC_SelectedCustomer, customer.Id);
        setSelectedCustomer(customer);
    };

    const handleCreateNew = () => {
        router.push('/property/edit/new');
    };

    const handleBackToList = () => {
        localStorage.removeItem(LocalStorageKeyEnum.JC_SelectedCustomer);
        setSelectedCustomer(null);
    };

    const handleSubmit = async () => {
        if (!selectedCustomer) return;

        try {
            setIsLoading(true);
            const response = await PropertyModel.Update(selectedCustomer);
            if (response) {
                // Refresh the data
                await loadData();
            }
        } catch (error) {
            console.error('Error saving customer:', error);
        } finally {
            setIsLoading(false);
        }
    };

    // - UTILITY FUNCTIONS - //
    const getBuildingTypeName = (code: string) => {
        const buildingType = buildingTypes.find(bt => bt.Code === code);
        return buildingType ? buildingType.Name : 'Not specified';
    };

    // - LIST CONFIGURATION - //
    const listHeaders: JC_ListHeader[] = [
        { sortKey: "Address", label: "Address" },
        { sortKey: "BuildingTypeCode", label: "Building Type" },
        { sortKey: "CompanyStrataTitleCode", label: "Company/Strata", hideOnSmall: true },
        { sortKey: "NumBedroomsCode", label: "Bedrooms", hideOnTiny: true },
        { sortKey: "CreatedAt", label: "Created", hideOnMedium: true }
    ];

    // Create the customer form tablet model when a customer is selected
    const createCustomerFormTablet = (): JC_FormTabletModel => {
        if (!selectedCustomer) throw new Error("No customer selected");

        return {
            headerLabel: `Customer - ${selectedCustomer.Address || "No Address"}`,
            leftPaneHeader: "Customer Fields",
            backButtonLink: undefined, // We'll handle back with our own button
            sections: [
                {
                    Heading: "Customer Details",
                    Fields: [
                        {
                            inputId: "address",
                            type: FieldTypeEnum.Text,
                            label: "Address",
                            value: selectedCustomer.Address || "",
                            onChange: (newValue) => setSelectedCustomer(prev => prev ? { ...prev, Address: newValue } : null),
                            validate: (value) => JC_Utils.stringNullOrEmpty(value?.toString()) ? "Address is required." : ""
                        }
                    ]
                }
            ],
            submitButtonText: "Save",
            onSubmit: handleSubmit,
            isLoading: isLoading,
            additionalFooterButtons: [
                {
                    text: "Back to List",
                    onClick: handleBackToList
                }
            ]
        };
    };

    // - BUILD CUSTOMER LIST - //
    function _buildCustomerList() {
        return (
            <div className={styles.propertyListContainer}>
                <div className={styles.createButtonContainer}>
                    <JC_Button
                        text="Create New Customer"
                        onClick={handleCreateNew}
                        isLoading={isLoading}
                    />
                </div>

                {!isLoading &&
                <JC_List
                    items={properties}
                    headers={listHeaders}
                    defaultSortKey="Address"
                    defaultSortDirection="asc"
                    row={(customer: PropertyModel) => (
                        <tr key={customer.Id} onClick={() => handleCustomerClick(customer)} className={styles.clickableRow}>
                            <td>{customer.Address || 'No address'}</td>
                            <td>{getBuildingTypeName(customer.BuildingTypeCode || '')}</td>
                            <td>{customer.CompanyStrataTitleCode || 'Not specified'}</td>
                            <td>{customer.NumBedroomsCode || 'Not specified'}</td>
                            <td>{customer.CreatedAt ? new Date(customer.CreatedAt).toLocaleDateString() : 'Unknown'}</td>
                        </tr>
                    )}
                />}
            </div>
        );
    }

    // - RENDER - //
    if (!initialised) {
        return <JC_Spinner isPageBody />;
    }

    // If a customer is selected, show the customer form
    if (selectedCustomer) {
        return <JC_FormTablet model={createCustomerFormTablet()} />;
    }

    // Otherwise show the customer list
    return (
        <div className={styles.mainContainer}>
            <JC_Title title="Customers" />
            {_buildCustomerList()}
        </div>
    );
}
